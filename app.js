/**
 * Hover Cards · Advanced Interactions
 * - 主题切换（浅/深）
 * - 进阶效果开关（3D 倾斜、视差、光斑）
 * - 鼠标位置驱动 transform 与渐变
 * - 触屏与 Reduced Motion 自动降级
 */

(function () {
    const docEl = document.documentElement;
    const enableAdvancedEl = document.getElementById('enableAdvanced');
    const toggleThemeBtn = document.getElementById('toggleTheme');

    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)');
    const prefersReduceMotion = window.matchMedia('(prefers-reduced-motion: reduce)');
    const isTouch = matchMedia('(hover: none) and (pointer: coarse)').matches;

    // 初始化主题：优先使用 localStorage，否则跟随系统
    const THEME_KEY = 'hover-cards-theme';
    const savedTheme = localStorage.getItem(THEME_KEY);
    if (savedTheme === 'light' || savedTheme === 'dark') {
        docEl.setAttribute('data-theme', savedTheme);
    } else {
        docEl.setAttribute('data-theme', prefersDark.matches ? 'dark' : 'light');
    }

    // 主题切换
    if (toggleThemeBtn) {
        toggleThemeBtn.addEventListener('click', () => {
            const current = docEl.getAttribute('data-theme') === 'dark' ? 'dark' : 'light';
            const next = current === 'dark' ? 'light' : 'dark';
            docEl.setAttribute('data-theme', next);
            localStorage.setItem(THEME_KEY, next);
            toggleThemeBtn.setAttribute('aria-pressed', String(next === 'dark'));
        });
    }

    // 进阶效果：注册/注销增强
    const advancedCards = Array.from(document.querySelectorAll('[data-advanced].card--advanced'));

    // 状态
    let advancedEnabled = false;
    // 如果用户勾选了开关则启用，否则默认启用（桌面且非 reduced-motion）
    const defaultAdvanced = !isTouch && !prefersReduceMotion.matches;
    if (enableAdvancedEl) {
        enableAdvancedEl.checked = defaultAdvanced;
        advancedEnabled = enableAdvancedEl.checked;
        enableAdvancedEl.addEventListener('change', () => {
            advancedEnabled = enableAdvancedEl.checked;
            if (advancedEnabled) {
                attachAdvanced();
            } else {
                detachAdvanced(true);
            }
        });
    } else {
        advancedEnabled = defaultAdvanced;
    }

    // Reduced Motion 变更监听：自动降级
    prefersReduceMotion.addEventListener('change', (e) => {
        if (e.matches) {
            detachAdvanced(true);
        } else if (enableAdvancedEl ? enableAdvancedEl.checked : defaultAdvanced) {
            attachAdvanced();
        }
    });

    // Hover/Tilt 参数
    const TILT_MAX_DEG = 8; // 与 CSS 变量 --tilt-max 一致
    const PARALLAX_SHIFT = 12; // px, 与 --parallax-shift 一致
    const RESET_EASING = 'cubic-bezier(.2,.8,.2,1)';
    const RESET_MS = 450;

    // 为每张卡片维护事件与状态
    const stateMap = new WeakMap();

    function attachAdvanced() {
        if (prefersReduceMotion.matches) return;
        advancedCards.forEach((card) => {
            if (stateMap.has(card)) return;
            const onMove = handlePointerMove.bind(null, card);
            const onEnter = handlePointerEnter.bind(null, card);
            const onLeave = handlePointerLeave.bind(null, card);
            card.addEventListener('pointermove', onMove);
            card.addEventListener('pointerenter', onEnter);
            card.addEventListener('pointerleave', onLeave);

            // 键盘焦点也启用轻量 3D，但不跟随
            const onFocus = () => applyTilt(card, 0, 0, true);
            const onBlur = () => resetCard(card);
            card.addEventListener('focus', onFocus);
            card.addEventListener('blur', onBlur);

            stateMap.set(card, { onMove, onEnter, onLeave, onFocus, onBlur });
        });
    }

    function detachAdvanced(reset = false) {
        advancedCards.forEach((card) => {
            const st = stateMap.get(card);
            if (!st) return;
            card.removeEventListener('pointermove', st.onMove);
            card.removeEventListener('pointerenter', st.onEnter);
            card.removeEventListener('pointerleave', st.onLeave);
            card.removeEventListener('focus', st.onFocus);
            card.removeEventListener('blur', st.onBlur);
            stateMap.delete(card);
            if (reset) resetCard(card);
        });
    }

    // 触屏：用点击切换“展开”态，第二次点击关闭；不做 3D/光斑
    if (isTouch) {
        advancedCards.forEach((card) => {
            card.addEventListener('click', () => {
                const expanded = card.getAttribute('data-expanded') === 'true';
                card.setAttribute('data-expanded', String(!expanded));
                // 展开时直接显式内容（样式中 hover/focus 已处理，触屏没有 hover）
                // 这里不做 transform 以避免眩晕
            });
        });
    } else {
        // 桌面按默认策略
        if (advancedEnabled) attachAdvanced();
    }

    // 计算卡片内归一化坐标与中心偏移
    function getCardMetrics(card, e) {
        const rect = card.getBoundingClientRect();
        const px = e.clientX - rect.left;
        const py = e.clientY - rect.top;
        const nx = (px / rect.width) * 2 - 1;  // [-1,1]
        const ny = (py / rect.height) * 2 - 1; // [-1,1]
        return { rect, px, py, nx, ny };
    }

    function handlePointerEnter(card, e) {
        if (!advancedEnabled || prefersReduceMotion.matches) return;
        card.style.transition = 'transform .2s ease';
        // 显示光泽层
        const gloss = card.querySelector('.card__gloss');
        if (gloss) {
            gloss.style.transition = 'opacity .3s ease';
            gloss.style.opacity = '1';
        }
    }

    function handlePointerLeave(card, e) {
        resetCard(card);
    }

    function resetCard(card) {
        card.style.transition = `transform ${RESET_MS}ms ${RESET_EASING}`;
        card.style.transform = '';
        const media = card.querySelector('.parallax');
        if (media) {
            media.style.transition = `transform ${RESET_MS}ms ${RESET_EASING}`;
            media.style.transform = 'translate3d(0,0,0)';
        }
        const gloss = card.querySelector('.card__gloss');
        if (gloss) {
            gloss.style.transition = `opacity ${RESET_MS}ms ${RESET_EASING}`;
            gloss.style.opacity = '0';
            gloss.style.backgroundPosition = '';
        }
    }

    // 节流：使用 rAF 避免频繁布局与样式抖动
    function rafThrottle(fn) {
        let ticking = false;
        return function (...args) {
            if (ticking) return;
            ticking = true;
            requestAnimationFrame(() => {
                ticking = false;
                fn.apply(this, args);
            });
        };
    }

    const handlePointerMove = rafThrottle((card, e) => {
        if (!advancedEnabled || prefersReduceMotion.matches) return;
        const { rect, nx, ny, px, py } = getCardMetrics(card, e);

        // 倾斜：y 控制 rotateX，x 控制 rotateY（视觉与直觉相符）
        const rx = (-ny) * TILT_MAX_DEG;
        const ry = (nx) * TILT_MAX_DEG;
        applyTilt(card, rx, ry);

        // 视差：封面做反向平移
        const media = card.querySelector('.parallax');
        if (media) {
            const tx = (-nx) * PARALLAX_SHIFT;
            const ty = (-ny) * PARALLAX_SHIFT;
            media.style.transform = `translate3d(${tx}px, ${ty}px, 0) scale(1.02)`;
        }

        // 光斑：根据鼠标位置更新 radial-gradient 中心
        const gloss = card.querySelector('.card__gloss');
        if (gloss) {
            const gx = ((px / rect.width) * 100).toFixed(2);
            const gy = ((py / rect.height) * 100).toFixed(2);
            gloss.style.background = `radial-gradient(var(--gloss-size) var(--gloss-size) at ${gx}% ${gy}%, rgba(255,255,255,var(--gloss-alpha)), transparent 60%)`;
            gloss.style.opacity = '1';
        }
    });

    function applyTilt(card, rx = 0, ry = 0, focusMode = false) {
        // focusMode 仅给予轻微立体感
        const factor = focusMode ? 0.5 : 1;
        card.style.transform = `rotateX(${rx * factor}deg) rotateY(${ry * factor}deg) scale(var(--card-scale))`;
        card.style.transformStyle = 'preserve-3d';
    }

    // 无障碍：键盘激活按钮不滚动页面
    document.addEventListener('keydown', (e) => {
        if (e.key === ' ' && e.target instanceof HTMLElement && e.target.classList.contains('card')) {
            e.preventDefault();
            e.target.click();
        }
    });
})();