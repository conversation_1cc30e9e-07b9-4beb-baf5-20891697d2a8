<!doctype html>
<html lang="zh-CN" data-theme="light">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1,viewport-fit=cover">
    <meta name="color-scheme" content="light dark">
    <title>Hover Card Demo · Elegant Dynamic Content</title>
    <link rel="stylesheet" href="styles.css">
</head>

<body>
    <header class="site-header">
        <h1 class="site-title">Hover Cards · 优雅悬停卡片</h1>
        <div class="actions">
            <button class="btn" id="toggleTheme" aria-pressed="false" title="切换深浅色">切换主题</button>
            <label class="switch">
                <input type="checkbox" id="enableAdvanced" aria-label="启用进阶效果（3D 与光斑）">
                <span>进阶效果</span>
            </label>
        </div>
    </header>

    <main class="container">
        <section aria-labelledby="basicTitle">
            <h2 id="basicTitle" class="section-title">基础款 · 仅 CSS 悬停动画</h2>
            <div class="grid">
                <article class="card card--basic" tabindex="0" aria-label="卡片：极简设计指南">
                    <div class="card__media"
                        style="--img:url('https://images.unsplash.com/photo-1529333166437-7750a6dd5a70?q=80&auto=format&fit=crop&w=1200&ixlib=rb-4.0.3');">
                    </div>
                    <div class="card__overlay"></div>
                    <div class="card__content">
                        <div class="card__tags">
                            <span class="tag">Design</span>
                            <span class="tag">Guide</span>
                        </div>
                        <h3 class="card__title">极简设计指南</h3>
                        <p class="card__desc">通过留白、层次与微动效提升信息可读性与专注度。</p>
                        <button class="btn btn--primary" aria-label="查看极简设计指南详情">查看详情</button>
                    </div>
                </article>

                <article class="card card--basic" tabindex="0" aria-label="卡片：前端性能清单">
                    <div class="card__media"
                        style="--img:url('https://images.unsplash.com/photo-1518779578993-ec3579fee39f?q=80&auto=format&fit=crop&w=1200&ixlib=rb-4.0.3');">
                    </div>
                    <div class="card__overlay"></div>
                    <div class="card__content">
                        <div class="card__tags">
                            <span class="tag">Web</span>
                            <span class="tag">Performance</span>
                        </div>
                        <h3 class="card__title">前端性能清单</h3>
                        <p class="card__desc">仅用 transform/opacity 动画，避免布局抖动与重排。</p>
                        <button class="btn btn--primary">查看详情</button>
                    </div>
                </article>

                <article class="card card--basic" tabindex="0" aria-label="卡片：无障碍与动效">
                    <div class="card__media"
                        style="--img:url('https://images.unsplash.com/photo-1529101091764-c3526daf38fe?q=80&auto=format&fit=crop&w=1200&ixlib=rb-4.0.3');">
                    </div>
                    <div class="card__overlay"></div>
                    <div class="card__content">
                        <div class="card__tags">
                            <span class="tag">A11y</span>
                        </div>
                        <h3 class="card__title">无障碍与动效</h3>
                        <p class="card__desc">尊重用户的“减少动效”偏好，自动降级动画强度。</p>
                        <button class="btn btn--primary">查看详情</button>
                    </div>
                </article>
            </div>
        </section>

        <section aria-labelledby="advTitle">
            <h2 id="advTitle" class="section-title">进阶款 · 3D 视差与光斑</h2>
            <div class="grid">
                <article class="card card--advanced" tabindex="0" aria-label="卡片：产品洞察报告" data-advanced>
                    <div class="card__media parallax"
                        style="--img:url('https://images.unsplash.com/photo-1600267175161-cfaa711b4a2d?q=80&auto=format&fit=crop&w=1200&ixlib=rb-4.0.3');">
                    </div>
                    <div class="card__gloss"></div>
                    <div class="card__overlay"></div>
                    <div class="card__content">
                        <div class="card__tags">
                            <span class="tag">Insights</span>
                            <span class="tag">Report</span>
                        </div>
                        <h3 class="card__title">产品洞察报告</h3>
                        <p class="card__desc">悬停时出现柔和 3D 倾斜与光斑，内容更具层次。</p>
                        <div class="inline-stats">
                            <div class="stat">
                                <span class="stat__label">增长</span>
                                <span class="stat__value">+18%</span>
                            </div>
                            <div class="stat">
                                <span class="stat__label">留存</span>
                                <span class="stat__value">42%</span>
                            </div>
                        </div>
                        <button class="btn btn--primary">查看报告</button>
                    </div>
                </article>

                <article class="card card--advanced" tabindex="0" aria-label="卡片：交互趋势分析" data-advanced>
                    <div class="card__media parallax"
                        style="--img:url('https://images.unsplash.com/photo-1451187580459-43490279c0fa?q=80&auto=format&fit=crop&w=1200&ixlib=rb-4.0.3');">
                    </div>
                    <div class="card__gloss"></div>
                    <div class="card__overlay"></div>
                    <div class="card__content">
                        <div class="card__tags">
                            <span class="tag">Interaction</span>
                            <span class="tag">Trends</span>
                        </div>
                        <h3 class="card__title">交互趋势分析</h3>
                        <p class="card__desc">磁性光斑跟随鼠标，配合视差与回弹，体验高级。</p>
                        <button class="btn btn--primary">打开分析</button>
                    </div>
                </article>
            </div>
            <p class="note">提示：切换顶部“进阶效果”开关以启用/禁用 3D 与光斑。触屏将自动降级。</p>
        </section>
    </main>

    <footer class="site-footer">
        <p>Demo 遵循 prefers-reduced-motion 与可访问性最佳实践。</p>
    </footer>

    <script src="app.js"></script>
</body>

</html>