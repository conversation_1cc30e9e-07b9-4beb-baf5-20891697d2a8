/* =========================
   Hover Cards · Elegant Dynamic Content
   技术要点：
   - 动画仅使用 transform / opacity（避免重排）
   - 使用 CSS 变量实现主题与参数化
   - 支持浅色/深色，并尊重系统色彩方案
   - prefers-reduced-motion 降级
   ========================= */

:root {
    --radius: 16px;
    --radius-sm: 12px;
    --gap: 20px;
    --page-max: 1100px;

    /* 色彩（浅色主题） */
    --bg: #f7f8fa;
    --bg-elev: #ffffff;
    --fg: #0b1220;
    --fg-subtle: #5b6477;
    --border: #e7e9ee;
    --shadow-rgb: 10 24 52;

    /* 主色与渐变 */
    --primary: #4f7cff;
    --primary-600: #3f68e6;
    --grad-from: #6fa7ff;
    --grad-to: #c2d1ff;

    /* 卡片动效参数 */
    --card-scale: 1.03;
    --media-dim: 0.15;
    /* 悬停时媒体暗化强度 */
    --overlay-opacity: 0.0;
    --overlay-opacity-hover: 0.25;
    --title-translate: 8px;
    --content-fade-duration: 300ms;
    --easing: cubic-bezier(.2, .8, .2, 1);
    --shadow-rest: 0 6px 18px rgba(var(--shadow-rgb) / 0.08);
    --shadow-hover: 0 16px 40px rgba(var(--shadow-rgb) / 0.18), 0 2px 6px rgba(var(--shadow-rgb) / 0.08);

    /* 高亮光斑（进阶） */
    --gloss-size: 180px;
    --gloss-alpha: 0.18;
    --tilt-max: 8deg;
    --parallax-shift: 12px;

    /* 字体 */
    --font: ui-sans-serif, system-ui, -apple-system, Segoe UI, Roboto, Noto Sans SC, "Helvetica Neue", "PingFang SC", "Hiragino Sans GB", Arial, "Microsoft YaHei", "Segoe UI Emoji", "Segoe UI Symbol";
}

@media (prefers-color-scheme: dark) {
    :root {
        --bg: #0b0f16;
        --bg-elev: #121826;
        --fg: #e9edf5;
        --fg-subtle: #a3adc2;
        --border: #1d2534;
        --shadow-rgb: 3 6 14;

        --primary: #7aa2ff;
        --primary-600: #97b6ff;
        --grad-from: #2847a8;
        --grad-to: #4d6ae0;

        --overlay-opacity-hover: 0.28;
        --media-dim: 0.25;
    }
}

/* 应用自定义 data-theme 切换 */
html[data-theme="dark"] {
    color-scheme: dark;
}

html[data-theme="light"] {
    color-scheme: light;
}

/* 全局基础 */
*,
*::before,
*::after {
    box-sizing: border-box;
}

html,
body {
    height: 100%;
}

body {
    margin: 0;
    font-family: var(--font);
    color: var(--fg);
    background: radial-gradient(1200px 800px at 10% -10%, rgba(79, 124, 255, 0.08), transparent 60%),
        radial-gradient(1000px 600px at 90% -20%, rgba(111, 167, 255, 0.06), transparent 60%),
        var(--bg);
}

.site-header,
.site-footer {
    max-width: var(--page-max);
    margin: 0 auto;
    padding: 20px var(--gap);
}

.site-title {
    font-size: 24px;
    margin: 6px 0 10px;
    letter-spacing: .2px;
}

.actions {
    display: flex;
    align-items: center;
    gap: 12px;
}

.btn {
    appearance: none;
    border: 1px solid var(--border);
    background: var(--bg-elev);
    color: var(--fg);
    padding: 8px 14px;
    border-radius: 10px;
    cursor: pointer;
    transition: background .2s ease, border-color .2s ease, transform .2s ease;
}

.btn:hover {
    transform: translateY(-1px);
}

.btn:active {
    transform: translateY(0);
}

.btn--primary {
    border: none;
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-600) 100%);
    color: #fff;
    box-shadow: 0 6px 16px rgba(79, 124, 255, .25);
}

.switch {
    display: inline-flex;
    gap: 8px;
    align-items: center;
    font-size: 14px;
    color: var(--fg-subtle);
}

.switch input {
    inline-size: 18px;
    block-size: 18px;
}

.container {
    max-width: var(--page-max);
    margin: 0 auto;
    padding: 10px var(--gap) 60px;
}

.section-title {
    font-size: 18px;
    margin: 24px 0 12px;
    color: var(--fg-subtle);
    font-weight: 600;
}

.note {
    margin: 10px 0 0;
    color: var(--fg-subtle);
    font-size: 14px;
}

/* 网格布局 */
.grid {
    display: grid;
    grid-template-columns: repeat(3, minmax(0, 1fr));
    gap: var(--gap);
}

@media (max-width: 980px) {
    .grid {
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }
}

@media (max-width: 640px) {
    .grid {
        grid-template-columns: 1fr;
    }
}

/* 卡片基础 */
.card {
    position: relative;
    overflow: clip;
    border-radius: var(--radius);
    background: var(--bg-elev);
    border: 1px solid var(--border);
    min-block-size: 260px;
    box-shadow: var(--shadow-rest);
    transform: translateZ(0);
    transition: transform .35s var(--easing), box-shadow .35s var(--easing);
    outline: none;
    will-change: transform;
}

.card:focus-visible {
    box-shadow: 0 0 0 3px color-mix(in oklab, var(--primary) 60%, transparent), var(--shadow-rest);
}

/* 媒体层（使用 CSS 变量注入图片） */
.card__media {
    position: absolute;
    inset: 0;
    background: center/cover no-repeat var(--bg-elev);
    background-image: var(--img);
    filter: saturate(1) contrast(1);
    transform: scale(1);
    transition: transform .6s var(--easing), opacity .4s var(--easing), filter .4s var(--easing);
}

/* 光泽层（进阶） */
.card__gloss {
    pointer-events: none;
    position: absolute;
    inset: 0;
    background: radial-gradient(var(--gloss-size) var(--gloss-size) at 50% 0%, rgba(255 255 255 / var(--gloss-alpha)), transparent 60%);
    opacity: 0;
    transition: opacity .4s var(--easing), background-position .2s linear;
    mix-blend-mode: soft-light;
}

/* 覆盖层（暗化与色彩氛围） */
.card__overlay {
    position: absolute;
    inset: 0;
    background: linear-gradient(to top, rgba(0, 0, 0, .45), rgba(0, 0, 0, .15) 40%, transparent 70%),
        linear-gradient(to top, rgba(79, 124, 255, .16), transparent 60%);
    opacity: var(--overlay-opacity);
    transition: opacity .4s var(--easing);
}

/* 内容层 */
.card__content {
    position: relative;
    display: grid;
    gap: 10px;
    padding: 18px;
    margin-top: auto;
    min-block-size: 60%;
    align-content: end;
    color: #fff;
    z-index: 1;
}

.card__tags {
    display: inline-flex;
    gap: 6px;
    flex-wrap: wrap;
    opacity: .0;
    transform: translateY(var(--title-translate));
    transition: transform var(--content-fade-duration) var(--easing), opacity var(--content-fade-duration) var(--easing);
}

.tag {
    font-size: 12px;
    padding: 3px 8px;
    border-radius: 999px;
    background: color-mix(in oklab, var(--primary) 34%, rgba(255, 255, 255, .1));
    border: 1px solid rgba(255, 255, 255, .22);
    backdrop-filter: blur(4px);
}

.card__title {
    font-size: 20px;
    line-height: 1.25;
    margin: 0;
    font-weight: 700;
    letter-spacing: .2px;
    opacity: .0;
    transform: translateY(calc(var(--title-translate) * 0.8));
    transition: transform var(--content-fade-duration) var(--easing), opacity var(--content-fade-duration) var(--easing);
}

.card__desc {
    margin: 0;
    font-size: 14px;
    color: #e8ecff;
    opacity: .0;
    transform: translateY(calc(var(--title-translate) * 0.6));
    transition: transform var(--content-fade-duration) var(--easing), opacity var(--content-fade-duration) var(--easing);
}

.inline-stats {
    display: inline-flex;
    gap: 14px;
    opacity: .0;
    transform: translateY(calc(var(--title-translate) * 0.4));
    transition: transform var(--content-fade-duration) var(--easing), opacity var(--content-fade-duration) var(--easing);
}

.stat {
    display: grid;
    gap: 4px;
    font-size: 12px;
    color: #e0e6ff;
}

.stat__value {
    font-size: 16px;
    font-weight: 700;
    color: #ffffff;
}

/* 悬停/聚焦效果（基础款与进阶款共享） */
.card:hover,
.card:focus-visible {
    transform: scale(var(--card-scale));
    box-shadow: var(--shadow-hover);
}

.card:hover .card__overlay,
.card:focus-visible .card__overlay {
    opacity: var(--overlay-opacity-hover);
}

.card:hover .card__media,
.card:focus-visible .card__media {
    filter: saturate(1.05) contrast(1.05) brightness(calc(1 - var(--media-dim)));
    transform: scale(1.06);
}

.card:hover .card__title,
.card:hover .card__tags,
.card:hover .card__desc,
.card:hover .inline-stats,
.card:focus-visible .card__title,
.card:focus-visible .card__tags,
.card:focus-visible .card__desc,
.card:focus-visible .inline-stats {
    opacity: 1;
    transform: translateY(0);
}

/* 基础款额外样式（无需 JS）*/
.card--basic .inline-stats {
    display: none;
}

.card--basic .card__gloss {
    display: none;
}

/* 进阶款默认样式（JS 会增强） */
.card--advanced {
    perspective: 1000px;
    transform-style: preserve-3d;
}

.card--advanced .parallax {
    will-change: transform;
    transition: transform .6s var(--easing);
}

/* 按钮在卡片内的适配 */
.card .btn {
    justify-self: start;
    padding: 8px 12px;
    font-size: 14px;
}

/* 无障碍与移动端降级处理 */

/* 1) 减少动效偏好：禁用放大、禁用 3D/视差，保留淡入 */
@media (prefers-reduced-motion: reduce) {

    .card,
    .card__media,
    .card__tags,
    .card__title,
    .card__desc,
    .inline-stats {
        transition: none !important;
    }

    .card:hover,
    .card:focus-visible {
        transform: none;
    }

    .card:hover .card__media,
    .card:focus-visible .card__media {
        transform: none;
        filter: none;
    }

    .card__tags,
    .card__title,
    .card__desc,
    .inline-stats {
        opacity: 1;
        transform: none;
    }

    .card__overlay {
        opacity: .15;
    }

    .card__gloss {
        display: none !important;
    }
}

/* 2) 触屏：移除 :hover 放大，由 JS 切换轻量状态 */
@media (hover: none) and (pointer: coarse) {
    .card:hover {
        transform: none;
    }

    .card:hover .card__media {
        transform: none;
    }
}

/* 辅助：小屏边距优化 */
@media (max-width: 420px) {

    .site-header,
    .site-footer {
        padding: 16px 16px;
    }

    .container {
        padding: 4px 16px 40px;
    }
}