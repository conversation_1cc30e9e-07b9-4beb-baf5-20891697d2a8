# 悬停动态卡片（Hover Cards）详细需求文档

版本信息
- 文档版本：v1.0.0
- 最近更新时间：2025-08-04
- 负责人：Kilo Code
- 状态：已实现基础款与进阶款 Demo，对应 [index.html](../index.html)、[styles.css](../styles.css)、[app.js](../app.js)

1. 背景与目标
1.1 背景
- 在信息密集型页面（如内容集合页、产品卡片、仪表盘小部件）中，用户需要快速浏览并在悬停时获取更多语义信息。
- 通过优雅的动效在不打断用户注意力的前提下，提升可读性与愉悦度，同时保证性能、可访问性与跨设备一致性。

1.2 目标
- 提供“基础款”与“进阶款”两种悬停卡片交互样式：
  - 基础款：纯 CSS 动画，轻量、安全、零 JS 依赖。
  - 进阶款：3D 倾斜、视差与磁性光斑，带来更高的沉浸感。
- 支持深浅色主题、用户“减少动效”偏好、触屏降级与键盘可达性。

1.3 非目标
- 本版本不提供数据拉取与动态内容管理。
- 不包含复杂图表/时间轴的完整实现，仅提供占位样例与样式扩展点。

2. 术语与定义
- 悬停（Hover）：指鼠标指针进入卡片可交互区域触发的视觉反馈。
- 进阶效果（Advanced）：包括 3D 倾斜、封面视差位移、跟随光斑（radial-gradient）等 JS 增强行为。
- 触屏降级：在 hover 不可用的设备上使用点击/轻量展开替代。
- Reduced Motion：系统级“减少动态效果”偏好，需自动关闭大幅动效。

3. 用户故事
- 访客（桌面端）：在内容列表上浏览卡片，希望悬停时快速看到标题、简述、标签与 CTA，必要时体验但不被打扰的高级动效。
- 无障碍用户（键盘/读屏）：希望通过 Tab 访问卡片，获得可见焦点与同等视觉反馈；读屏可理解卡片信息层级。
- 移动端用户（触屏）：无悬停，点击卡片可轻量展开/聚焦主要内容，不出现眩晕动效。
- 内容运营：可快速换图与文案，卡片结构与样式不需要修改 JS 逻辑即可适配。

4. 功能需求
4.1 基础款卡片（必选）
- R1. 结构：封面图、覆盖层（渐变/暗化）、内容区（标签、标题、副标题/描述、CTA）。
- R2. 动效：悬停时卡片微缩放（默认 1.03）、阴影增强、封面饱和度/亮度调整；文本由下至上浮现（opacity + translateY）。
- R3. 应用范围：整张卡片区域悬停触发；键盘 focus-visible 触发同等效果。
- R4. 性能：仅使用 transform/opacity 参与动画，避免 layout/reflow。
- R5. 主题：支持浅/深色主题自动与手动切换。

4.2 进阶款卡片（可选增强）
- R6. 3D 倾斜：根据指针位置在 [-8°, 8°] 范围内 rotateX/rotateY 倾斜。
- R7. 视差位移：封面图作与指针相反方向平移（默认最大位移 12px），并可叠加轻微 scale。
- R8. 光斑效果：radial-gradient 光泽层跟随指针位置，透明度与位置平滑过渡。
- R9. 回弹与复位：指针离开或失焦后，在 450ms 内平滑复位。
- R10. 开关控制：顶部开关启用/禁用进阶效果；桌面端默认启用、触屏与 Reduced Motion 默认关闭。

4.3 交互与无障碍
- R11. 键盘可达：卡片可聚焦（tabindex=0），:focus-visible 提供轮廓与动效。
- R12. ARIA：关键可交互元素包含可读标签（aria-label）；开关按钮具备 aria-pressed 等状态。
- R13. 触屏降级：点击卡片切换“展开”态，避免 3D 倾斜/跟随光斑。
- R14. Reduced Motion：关闭 3D、视差与缩放，仅保留渐变与内容可读性。CSS 与 JS 同时适配。
- R15. 可读性：保证文字对比度（暗层 + 文字颜色）；对小字号文本注意对比与清晰度。

4.4 主题与自定义
- R16. 自定义属性：通过 CSS 变量暴露关键参数，例如：
  - --card-scale、--media-dim、--overlay-opacity-hover、--content-fade-duration、--easing
  - --gloss-size、--gloss-alpha、--tilt-max、--parallax-shift
- R17. 深浅色：支持 prefers-color-scheme 与按钮切换，持久化 localStorage。
- R18. 扩展性：支持在 .card__content 中增减模块（如统计、标签云、进度条），沿用统一浮现动效。

5. 非功能性需求
5.1 性能
- F1. 初次渲染：不阻塞主线程；CSS/JS 体积轻量，无外部依赖。
- F2. 动画流畅度：在现代桌面浏览器中 60fps；批量卡片时不显著掉帧。
- F3. 资源：建议封面图 Web 优化（合适尺寸、自动格式），避免超高分辨率。

5.2 兼容性
- F4. 浏览器：Chromium/Firefox/Safari 最近两个主版本。
- F5. 设备：桌面与主流移动设备；触屏降级生效。
- F6. 降级策略：Reduced Motion 与 hover: none 环境必须功能完备、可读性不受影响。

5.3 可维护性
- F7. 结构化：HTML 语义合理、CSS 使用 BEM 风格与自定义属性、JS 采用事件解绑/复位。
- F8. 文档化：README 提供参数、主题、集成说明；注释明确。

6. 交互细节与动效参数
6.1 动效参数（默认值，可覆盖）
- P1. 悬停缩放：--card-scale = 1.03
- P2. 封面暗化：--media-dim = 0.15（深色 0.25）
- P3. 文本浮现：translateY 8px → 0，opacity 0 → 1，时长 300ms，缓动 cubic-bezier(.2,.8,.2,1)
- P4. 阴影增强：rest → hover（见 CSS 中 --shadow-rest / --shadow-hover）
- P5. 3D 倾斜：--tilt-max = 8deg（键盘 focus 模式取 50% 强度）
- P6. 视差位移：--parallax-shift = 12px
- P7. 光斑：--gloss-size = 180px、--gloss-alpha = 0.18
- P8. 回弹复位：450ms，cubic-bezier(.2,.8,.2,1)

6.2 手势/状态图
- Idle：无交互，基础图文安置。
- Hover/Focus：卡片 scale/阴影加深，封面饱和度/亮度变化，文本模块顺序浮现。
- Advanced-Hover（桌面）：在 Hover 基础上增加 3D 倾斜、视差与光斑跟随。
- Touch-Expanded（触屏）：点击卡片切换 data-expanded 状态，显示更多文本/CTA，不进行 3D 倾斜。

7. 信息架构与组件结构
7.1 DOM 结构
- .card（[index.html](../index.html) 中示例）
  - .card__media（封面，使用 --img 注入）
  - .card__gloss（进阶款光泽层，可选）
  - .card__overlay（渐变/暗层）
  - .card__content（内容区：.card__tags、.card__title、.card__desc、.inline-stats、按钮）

7.2 样式组织
- 统一在 [styles.css](../styles.css) 中定义主题与参数变量，分区注释清晰。
- 所有动画过渡尽量使用 transform/opacity，禁用昂贵属性。

7.3 行为逻辑
- 进阶款识别：.card.card--advanced[data-advanced]
- 事件绑定：pointerenter/leave/move + rAF 节流；focus/blur 复用轻量 tilt。
- 降级条件：hover: none 或 prefers-reduced-motion: reduce 自动关闭增强效果。

8. API 与可配置项
8.1 CSS 变量（节选）
- 视觉：--primary、--bg、--fg、--border、--shadow-rest、--shadow-hover 等
- 动效：--card-scale、--title-translate、--content-fade-duration、--easing
- 进阶：--tilt-max、--parallax-shift、--gloss-size、--gloss-alpha

8.2 JS 配置点（通过代码修改）
- TILT_MAX_DEG、PARALLAX_SHIFT、RESET_MS 与缓动曲线
- 默认开关策略：桌面默认启用进阶，触屏与 Reduced Motion 默认关闭

9. 可访问性（A11y）
- A1. 卡片可通过 Tab 聚焦；:focus-visible 提供明显边框/阴影。
- A2. 控件（按钮/开关）具备 aria-pressed、aria-label 等属性。
- A3. Reduced Motion 下：关闭 3D/视差/缩放；保留淡入与文字可读性。
- A4. 读屏顺序与视觉层级一致；标签语义清晰（如“查看详情”）。

10. 性能与测试
10.1 性能
- 使用 will-change: transform 优化关键层；谨慎使用以防长期占用合成层。
- 大图使用合适尺寸与格式；避免多层复杂滤镜叠加。

10.2 测试用例（人工验证）
- T1. 桌面 Chrome/Safari/Firefox：基础与进阶动效流畅，无抖动。
- T2. 移动浏览器：无 hover，点击切换展开；无 3D/光斑。
- T3. Reduced Motion：无 3D/视差/缩放，文本直接可见，不影响信息密度。
- T4. 键盘导航：Tab 至卡片与按钮，焦点样式明显，Space 不触发页面滚动。
- T5. 深浅色切换：UI 与文案可读性一致，光斑与阴影强度合理。
- T6. 多卡片网格：不少于 6 张卡片情况下滚动与悬停仍顺畅。

11. 里程碑与交付
- M1. 基础款与进阶款 Demo（已完成）
- M2. 需求文档（当前文档）
- M3. 组件化输出（可选）：提炼为框架组件（React/Vue/Svelte）与 Storybook（后续版本）

12. 风险与规避
- RISK-1：过强动效导致眩晕 → 将 --tilt-max 下调至 4deg、--gloss-alpha 至 0.12，并鼓励开启 Reduced Motion。
- RISK-2：批量卡片性能问题 → 控制图片尺寸、限制同屏卡片数量，必要时懒加载图片。
- RISK-3：可访问性被忽视 → 强制键盘路径测试，Lint 与人工验收并行。

13. 附录
13.1 文件参考
- 页面结构与示例：[index.html](../index.html)
- 样式参数与主题：[styles.css](../styles.css)
- 行为逻辑（进阶）：[app.js](../app.js)
- 使用说明：[README.md](../README.md)

13.2 变更记录
- 2025-08-04：v1.0.0 首版需求文档，覆盖基础与进阶效果、A11y、性能与测试清单。